'use client';
import { RoomForm } from '@/app/[locale]/bottin/locaux/form/room-form';
import { LoadingResource } from '@/components/loading-resource/loading-resource';
import { useGetGenericById } from '@/hooks/bottin/generic-list.hook';
import { useUpdateRoom } from '@/hooks/bottin/rooms.hook';
import type { RoomFormSchema } from '@/schemas/bottin/room-form-schema';
import type { RoomFormSectionKey } from '@/types/bottin/room';
import type { SupportedLocale } from '@/types/locale';
import { RoomEditToFormSchema } from '@rie/domain/serializers';
import * as Schema from 'effect/Schema';

type EditRoomPageParams = {
  formSections: Record<RoomFormSectionKey, string>;
  id: string;
  locale: SupportedLocale;
};
export default function EditionRoomPage({
  formSections,
  id,
}: EditRoomPageParams) {
  const {
    data: room,
    error,
    isPending,
  } = useGetGenericById<'local', 'edit'>({
    controlledListKey: 'local',
    id,
    view: 'edit',
  });

  if (isPending) {
    return <LoadingResource />;
  }

  if (error) {
    return <div className="p-4 text-red-500">Erreur : {error.message}</div>;
  }

  const updateRoom = useUpdateRoom();
  const onSubmit = async (data: RoomFormSchema) => {
    await updateRoom.mutateAsync({ id, payload: data });
  };

  // Transform room edit data to form schema using serializer
  const formData = Schema.decodeSync(RoomEditToFormSchema)(
    room,
  ) as RoomFormSchema;

  return (
    <RoomForm
      defaultValues={formData}
      formSections={formSections}
      onSubmit={onSubmit}
    />
  );
}
